import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Shield<PERSON>he<PERSON>, Clock, Award, CheckCircle2, AlertTriangle, Beaker, Phone, Mail, MapPin } from 'lucide-react'
import logo from './assets/1111.png'
import benefitsImage from '.generated-image.png'
import productImage from '.IMG_1792.jpg'
import './App.css'

function App() {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    telefone: '',
    mensagem: ''
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
    alert('Obrigado pelo contato! Entraremos em contato em breve.')
    setFormData({ nome: '', email: '', telefone: '', mensagem: '' })
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="landing-page">
      {/* Header/Navbar */}
      <header className="header">
        <div className="container">
          <div className="header-content">
            <img src={logo} alt="SafeDrink Labs" className="logo" />
            <nav className="nav">
              <a href="#produto" className="nav-link">Produto</a>
              <a href="#beneficios" className="nav-link">Benefícios</a>
              <a href="#como-funciona" className="nav-link">Como Funciona</a>
              <a href="#precos" className="nav-link">Preços</a>
              <a href="#contato" className="nav-link">Contato</a>
            </nav>
            <Button className="cta-button">Comprar Agora</Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">
                Proteja-se contra o <span className="highlight">Metanol</span>
              </h1>
              <p className="hero-subtitle">
                Teste rápido e confiável para detectar contaminação por metanol em bebidas alcoólicas. 
                Resultado em apenas 15 segundos com 99% de precisão.
              </p>
              <div className="hero-buttons">
                <Button size="lg" className="primary-button">
                  <ShieldCheck className="button-icon" />
                  Comprar Agora
                </Button>
                <Button size="lg" variant="outline" className="secondary-button">
                  Saiba Mais
                </Button>
              </div>
              <div className="hero-stats">
                <div className="stat">
                  <Clock className="stat-icon" />
                  <div>
                    <div className="stat-value">15s</div>
                    <div className="stat-label">Resultado Rápido</div>
                  </div>
                </div>
                <div className="stat">
                  <ShieldCheck className="stat-icon" />
                  <div>
                    <div className="stat-value">99%</div>
                    <div className="stat-label">Precisão</div>
                  </div>
                </div>
                <div className="stat">
                  <Award className="stat-icon" />
                  <div>
                    <div className="stat-value">30 dias</div>
                    <div className="stat-label">Garantia</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="hero-image">
              <img src={productImage} alt="Produtos SafeDrink Labs" className="product-image" />
            </div>
          </div>
        </div>
      </section>

      {/* Alerta Section */}
      <section className="alert-section">
        <div className="container">
          <div className="alert-box">
            <AlertTriangle className="alert-icon" />
            <div className="alert-content">
              <h3 className="alert-title">Por que você precisa do SafeDrink Labs?</h3>
              <p className="alert-text">
                O metanol é uma substância tóxica que pode causar sérios danos à saúde, incluindo cegueira e até morte. 
                Infelizmente, bebidas adulteradas com metanol são uma realidade em muitos estabelecimentos. 
                Proteja-se e sua família com nosso teste rápido e confiável.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefícios Section */}
      <section id="beneficios" className="benefits-section">
        <div className="container">
          <h2 className="section-title">Por que escolher SafeDrink Labs?</h2>
          <p className="section-subtitle">
            Tecnologia de ponta para sua segurança e tranquilidade
          </p>
          <div className="benefits-grid">
            <div className="benefit-card">
              <div className="benefit-icon-wrapper">
                <Clock className="benefit-icon" />
              </div>
              <h3 className="benefit-title">Resultado Rápido</h3>
              <p className="benefit-description">
                Detecção em apenas 15 segundos. Não perca tempo e tenha a resposta imediatamente.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon-wrapper">
                <ShieldCheck className="benefit-icon" />
              </div>
              <h3 className="benefit-title">99% de Precisão</h3>
              <p className="benefit-description">
                Certificado em laboratórios especializados. Confiança total nos resultados.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon-wrapper">
                <Beaker className="benefit-icon" />
              </div>
              <h3 className="benefit-title">Fácil de Usar</h3>
              <p className="benefit-description">
                Instruções simples em português. Qualquer pessoa pode realizar o teste.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon-wrapper">
                <Award className="benefit-icon" />
              </div>
              <h3 className="benefit-title">Garantia de 30 Dias</h3>
              <p className="benefit-description">
                Todos os produtos com garantia completa. Sua satisfação é nossa prioridade.
              </p>
            </div>
          </div>
          <div className="benefits-image-wrapper">
            <img src={benefitsImage} alt="Benefícios SafeDrink Labs" className="benefits-image" />
          </div>
        </div>
      </section>

      {/* Como Funciona Section */}
      <section id="como-funciona" className="how-it-works-section">
        <div className="container">
          <h2 className="section-title">Como Funciona</h2>
          <p className="section-subtitle">
            Simples, rápido e eficaz em 3 passos
          </p>
          <div className="steps-grid">
            <div className="step-card">
              <div className="step-number">1</div>
              <h3 className="step-title">Colete a Amostra</h3>
              <p className="step-description">
                Coloque uma pequena quantidade da bebida no recipiente de teste fornecido.
              </p>
            </div>
            <div className="step-card">
              <div className="step-number">2</div>
              <h3 className="step-title">Aguarde 15 Segundos</h3>
              <p className="step-description">
                O teste reage com a bebida e detecta a presença de metanol automaticamente.
              </p>
            </div>
            <div className="step-card">
              <div className="step-number">3</div>
              <h3 className="step-title">Leia o Resultado</h3>
              <p className="step-description">
                Verifique a mudança de cor no indicador. Instruções claras em português.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Preços Section */}
      <section id="precos" className="pricing-section">
        <div className="container">
          <h2 className="section-title">Escolha seu Kit</h2>
          <p className="section-subtitle">
            Planos para todas as necessidades
          </p>
          <div className="pricing-grid">
            <div className="pricing-card">
              <div className="pricing-header">
                <h3 className="pricing-title">20 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">29</span>
                  <span className="period">,90</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>20 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
              </ul>
              <Button className="pricing-button">Comprar Agora</Button>
            </div>

            <div className="pricing-card">
              <div className="pricing-header">
                <h3 className="pricing-title">40 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">54</span>
                  <span className="period">,90</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>40 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
              </ul>
              <Button className="pricing-button">Comprar Agora</Button>
            </div>

            <div className="pricing-card featured">
              <div className="featured-badge">Mais Popular</div>
              <div className="pricing-header">
                <h3 className="pricing-title">100 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">129</span>
                  <span className="period">,90</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>100 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Frete grátis</span>
                </li>
              </ul>
              <Button className="pricing-button featured-button">Comprar Agora</Button>
            </div>

            <div className="pricing-card">
              <div className="pricing-header">
                <h3 className="pricing-title">Caixa com 500 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">399</span>
                  <span className="period">,00</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>500 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Frete grátis</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Suporte prioritário</span>
                </li>
              </ul>
              <Button className="pricing-button">Comprar Agora</Button>
            </div>

            <div className="pricing-card">
              <div className="pricing-header">
                <h3 className="pricing-title">Caixa com 1000 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">699</span>
                  <span className="period">,00</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>1000 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Frete grátis</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Suporte prioritário</span>
                </li>
              </ul>
              <Button className="pricing-button">Comprar Agora</Button>
            </div>

            <div className="pricing-card">
              <div className="pricing-header">
                <h3 className="pricing-title">Caixa com 5000 Unidades</h3>
                <div className="pricing-price">
                  <span className="currency">R$</span>
                  <span className="amount">2.995</span>
                  <span className="period">,00</span>
                </div>
              </div>
              <ul className="pricing-features">
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>5000 testes inclusos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Resultado em 15 segundos</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Instruções em português</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Garantia de 30 dias</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Frete grátis</span>
                </li>
                <li className="pricing-feature">
                  <CheckCircle2 className="feature-icon" />
                  <span>Suporte prioritário</span>
                </li>
              </ul>
              <Button className="pricing-button">Comprar Agora</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Contato Section */}
      <section id="contato" className="contact-section">
        <div className="container">
          <div className="contact-grid">
            <div className="contact-info">
              <h2 className="section-title">Entre em Contato</h2>
              <p className="contact-description">
                Tem dúvidas? Nossa equipe está pronta para ajudar você a escolher o melhor kit para suas necessidades.
              </p>
              <div className="contact-methods">
                <div className="contact-method">
                  <Phone className="contact-icon" />
                  <div>
                    <div className="contact-label">Telefone</div>
                    <div className="contact-value">(11) 98765-4321</div>
                  </div>
                </div>
                <div className="contact-method">
                  <Mail className="contact-icon" />
                  <div>
                    <div className="contact-label">Email</div>
                    <div className="contact-value"><EMAIL></div>
                  </div>
                </div>
                <div className="contact-method">
                  <MapPin className="contact-icon" />
                  <div>
                    <div className="contact-label">Endereço</div>
                    <div className="contact-value">São Paulo, SP - Brasil</div>
                  </div>
                </div>
              </div>
            </div>
            <div className="contact-form-wrapper">
              <form onSubmit={handleSubmit} className="contact-form">
                <div className="form-group">
                  <label htmlFor="nome" className="form-label">Nome</label>
                  <input
                    type="text"
                    id="nome"
                    name="nome"
                    value={formData.nome}
                    onChange={handleChange}
                    className="form-input"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="form-input"
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="telefone" className="form-label">Telefone</label>
                  <input
                    type="tel"
                    id="telefone"
                    name="telefone"
                    value={formData.telefone}
                    onChange={handleChange}
                    className="form-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="mensagem" className="form-label">Mensagem</label>
                  <textarea
                    id="mensagem"
                    name="mensagem"
                    value={formData.mensagem}
                    onChange={handleChange}
                    className="form-textarea"
                    rows="4"
                    required
                  ></textarea>
                </div>
                <Button type="submit" className="form-submit">
                  Enviar Mensagem
                </Button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <img src={logo} alt="SafeDrink Labs" className="footer-logo" />
              <p className="footer-text">
                Protegendo vidas através da tecnologia e inovação.
              </p>
            </div>
            <div className="footer-section">
              <h4 className="footer-title">Links Rápidos</h4>
              <ul className="footer-links">
                <li><a href="#produto">Produto</a></li>
                <li><a href="#beneficios">Benefícios</a></li>
                <li><a href="#como-funciona">Como Funciona</a></li>
                <li><a href="#precos">Preços</a></li>
              </ul>
            </div>
            <div className="footer-section">
              <h4 className="footer-title">Suporte</h4>
              <ul className="footer-links">
                <li><a href="#contato">Contato</a></li>
                <li><a href="#">FAQ</a></li>
                <li><a href="#">Política de Privacidade</a></li>
                <li><a href="#">Termos de Uso</a></li>
              </ul>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2025 SafeDrink Labs. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
