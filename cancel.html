<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compra Cancelada - SafeDrink Labs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .cancel-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
            padding: 3rem;
            text-align: center;
            animation: fadeInUp 0.6s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .cancel-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ff9800, #ffc107);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: scaleIn 0.5s ease 0.3s both;
        }

        @keyframes scaleIn {
            from {
                transform: scale(0);
            }
            to {
                transform: scale(1);
            }
        }

        .cancel-icon svg {
            width: 60px;
            height: 60px;
            color: white;
        }

        .cancel-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 1rem;
        }

        .cancel-message {
            font-size: 1.1rem;
            color: #546e7a;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .reasons-box {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .reasons-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2C3E50;
            margin-bottom: 1rem;
        }

        .reason-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem 0;
            color: #546e7a;
        }

        .reason-item svg {
            width: 20px;
            height: 20px;
            color: #7CB342;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            margin: 0.5rem;
        }

        .btn-primary {
            background: #7CB342;
            color: white;
        }

        .btn-primary:hover {
            background: #558B2F;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
        }

        .btn-secondary {
            background: white;
            color: #2C3E50;
            border: 2px solid #2C3E50;
        }

        .btn-secondary:hover {
            background: #2C3E50;
            color: white;
            transform: translateY(-2px);
        }

        .help-box {
            background: #e3f2fd;
            border-left: 4px solid #1E88E5;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 2rem;
            text-align: left;
        }

        .help-box p {
            color: #546e7a;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .help-box a {
            color: #1E88E5;
            font-weight: 600;
            text-decoration: none;
        }

        .help-box a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .cancel-container {
                padding: 2rem;
            }

            .cancel-title {
                font-size: 1.5rem;
            }

            .cancel-message {
                font-size: 1rem;
            }

            .btn {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="cancel-container">
        <div class="cancel-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        </div>

        <h1 class="cancel-title">Compra Cancelada</h1>
        <p class="cancel-message">
            Sua compra foi cancelada e nenhum valor foi cobrado. Você pode tentar novamente quando quiser!
        </p>

        <div class="reasons-box">
            <h3 class="reasons-title">Por que escolher SafeDrink Labs?</h3>
            <div class="reason-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Resultado em apenas 15 segundos</span>
            </div>
            <div class="reason-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>99% de precisão certificada</span>
            </div>
            <div class="reason-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Garantia de 30 dias</span>
            </div>
            <div class="reason-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Pagamento 100% seguro</span>
            </div>
            <div class="reason-item">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Entrega rápida em todo o Brasil</span>
            </div>
        </div>

        <div>
            <a href="index.html#precos" class="btn btn-primary">Tentar Novamente</a>
            <a href="index.html" class="btn btn-secondary">Voltar para a Página Inicial</a>
        </div>

        <div class="help-box">
            <p>
                <strong>💬 Precisa de Ajuda?</strong><br>
                Se você teve algum problema durante o processo de compra ou tem dúvidas, 
                nossa equipe está pronta para ajudar!<br>
                <a href="mailto:<EMAIL>"><EMAIL></a> | 
                <a href="https://wa.me/5511999999999">WhatsApp</a>
            </p>
        </div>
    </div>

    <script>
        // Enviar evento de cancelamento (Google Analytics, Facebook Pixel, etc.)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'checkout_cancelled', {
                event_category: 'ecommerce',
                event_label: 'User cancelled checkout'
            });
        }

        // Extrair informações da URL se disponíveis
        const params = new URLSearchParams(window.location.search);
        const reason = params.get('reason');
        
        if (reason) {
            console.log('Motivo do cancelamento:', reason);
        }
    </script>
</body>
</html>

