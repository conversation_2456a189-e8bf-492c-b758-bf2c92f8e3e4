# 🚀 Guia de Configuração - Checkout Zedy + Pagloop

## ✅ Status da Integração

**CONFIGURAÇÃO COMPLETA E PRONTA PARA USO!**

Todos os 5 produtos estão configurados e funcionando com os links de checkout do Zedy.

---

## 📦 Produtos Configurados

| # | Produto | Preço | Link de Checkout |
|---|---------|-------|------------------|
| 1 | Kit 20 Unidades | R$ 29,90 | `product=1498917457139` |
| 2 | Kit 40 Unidades | R$ 54,90 | `product=1498917896952` |
| 3 | Kit 100 Unidades | R$ 129,90 | `product=1498981865111` |
| 4 | Caixa 500 Unidades | R$ 399,00 | `product=1498914285283` |
| 5 | Caixa 1000 Unidades | R$ 699,00 | `product=1498944824789` |

**Store ID:** `14989`

---

## 🔗 Links de Checkout Configurados

```javascript
'20-units':   https://zedy.com.br/api/public/shopify?product=1498917457139&store=14989
'40-units':   https://zedy.com.br/api/public/shopify?product=1498917896952&store=14989
'100-units':  https://zedy.com.br/api/public/shopify?product=1498981865111&store=14989
'500-units':  https://zedy.com.br/api/public/shopify?product=1498914285283&store=14989
'1000-units': https://zedy.com.br/api/public/shopify?product=1498944824789&store=14989
```

---

## 🔄 Fluxo de Compra

```
1. Cliente acessa a landing page (index.html)
   ↓
2. Navega até a seção de preços (#precos)
   ↓
3. Clica em "Comprar Agora" no produto desejado
   ↓
4. JavaScript redireciona para o link do Zedy
   ↓
5. Cliente preenche dados no checkout Zedy
   ↓
6. Pagamento processado via Pagloop
   ↓
7. Redirecionamento:
   ✅ Sucesso → success.html
   ❌ Cancelamento → cancel.html
```

---

## 🧪 Como Testar

### Opção 1: Teste Rápido na Landing Page

1. Abra `index.html` no navegador
2. Role até a seção "Escolha seu Kit"
3. Clique em "Comprar Agora" em qualquer produto
4. Você será redirecionado para o checkout do Zedy

### Opção 2: Teste com Debug

1. Abra `test-checkout.html` no navegador
2. Abra o Console (F12)
3. Clique em "Testar Checkout" em qualquer produto
4. Veja os dados no console antes de redirecionar
5. Confirme o redirecionamento

### Verificar no Console

Ao clicar em "Comprar Agora", você verá no console:

```
🛒 Iniciando checkout:
  produto: "Kit 20 Unidades"
  preco: "R$ 29,90"
  url: "https://zedy.com.br/api/public/shopify?product=1498917457139&store=14989"
```

---

## ⚙️ Configuração do Gateway Pagloop no Zedy

### Credenciais Pagloop

- **Chave Pública:** `pk_SrSHn_wcqgI-ED8wRthOgZ-QEuU4Hr1-z4MS2fN7AwVC7WNE`
- **Chave Privada:** `sk_hgEg6RARGBXibr9LwSBg3nvxnOmfhRmCpYAOLDeWco0AxFgD`
- **Ambiente:** Produção

### Passos para Configurar no Zedy

1. **Acesse o painel Zedy:**
   - URL: https://app.zedy.com.br
   - Email: <EMAIL>
   - Senha: Qwe121212.

2. **Configure o Gateway Pagloop:**
   - Vá em: Configurações → Gateways de Pagamento
   - Adicione: Pagloop
   - Insira as credenciais acima
   - Ative o gateway

3. **Verifique os Produtos:**
   - Vá em: Produtos
   - Confirme que os 5 produtos estão cadastrados
   - Verifique se os preços estão corretos

4. **Configure URLs de Retorno:**
   - URL de Sucesso: `https://seudominio.com/success.html`
   - URL de Cancelamento: `https://seudominio.com/cancel.html`

---

## 📊 Rastreamento e Analytics

### Google Analytics 4

O sistema rastreia automaticamente:

```javascript
gtag('event', 'begin_checkout', {
  currency: 'BRL',
  value: 29.90,
  items: [{
    item_id: 'kit-20-unidades',
    item_name: 'Kit 20 Unidades',
    item_category: 'Teste de Metanol',
    price: 29.90,
    quantity: 1
  }]
});
```

### Facebook Pixel

```javascript
fbq('track', 'InitiateCheckout', {
  content_name: 'Kit 20 Unidades',
  content_ids: ['kit-20-unidades'],
  content_type: 'product',
  value: 29.90,
  currency: 'BRL'
});
```

---

## 🔒 Segurança

### ⚠️ IMPORTANTE

- ✅ A chave **pública** (`pk_...`) é usada no frontend (seguro)
- ❌ A chave **privada** (`sk_...`) deve estar APENAS no backend do Zedy
- ✅ Nunca exponha a chave privada no código frontend
- ✅ Os links de checkout são públicos e seguros

---

## 🐛 Troubleshooting

### Problema: Redireciona para página inicial do Zedy

**Solução:** Verifique se:
- Os produtos estão ativos no painel Zedy
- O gateway Pagloop está configurado e ativo
- Os IDs dos produtos estão corretos

### Problema: Erro 404 no checkout

**Solução:**
- Confirme que o `store=14989` está correto
- Verifique se os produtos não foram deletados

### Problema: Pagamento não processa

**Solução:**
- Verifique as credenciais do Pagloop no painel Zedy
- Confirme que o gateway está em modo produção
- Teste com um produto de menor valor primeiro

---

## 📱 Responsividade

A landing page está 100% responsiva:

- ✅ Desktop (1920px+)
- ✅ Laptop (1024px - 1920px)
- ✅ Tablet (768px - 1024px)
- ✅ Mobile (320px - 768px)

O checkout do Zedy também é responsivo.

---

## 🎨 Personalização

### Alterar Preços

Edite no `index.html`:

```javascript
const PRODUCTS = {
  '20-units': {
    price: 2990, // em centavos (R$ 29,90)
    // ...
  }
}
```

**⚠️ Importante:** Altere também no painel do Zedy!

### Adicionar Novo Produto

1. Crie o produto no painel Zedy
2. Copie o link de checkout
3. Adicione em `ZEDY_CHECKOUT_LINKS`
4. Adicione em `PRODUCTS`
5. Crie um novo card na seção de preços

---

## 📞 Suporte

### Zedy
- Painel: https://app.zedy.com.br
- Email: <EMAIL>

### Pagloop
- Documentação: https://app.pagloop.com/docs/intro/first-steps
- Integrações: https://app.pagloop.com/integrations

---

## ✅ Checklist de Lançamento

Antes de colocar no ar:

- [x] Produtos configurados no Zedy
- [x] Links de checkout atualizados
- [x] Gateway Pagloop configurado
- [ ] URLs de retorno configuradas (success/cancel)
- [ ] Testar compra completa (ponta a ponta)
- [ ] Configurar Google Analytics
- [ ] Configurar Facebook Pixel
- [ ] Testar em mobile
- [ ] Verificar emails de confirmação
- [ ] Testar diferentes formas de pagamento

---

## 🚀 Próximos Passos

1. **Teste Completo:**
   - Faça uma compra teste de cada produto
   - Verifique se o email de confirmação chega
   - Teste cancelamento

2. **Configure Analytics:**
   - Adicione o código do Google Analytics
   - Configure o Facebook Pixel
   - Teste os eventos de conversão

3. **Otimize:**
   - Adicione depoimentos de clientes
   - Inclua fotos reais dos produtos
   - Crie vídeos demonstrativos

4. **Marketing:**
   - Configure campanhas no Google Ads
   - Crie anúncios no Facebook/Instagram
   - Prepare email marketing

---

**Última atualização:** 2025-01-15  
**Versão:** 2.0 - Links Configurados  
**Status:** ✅ PRONTO PARA PRODUÇÃO

