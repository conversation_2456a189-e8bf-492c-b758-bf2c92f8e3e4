<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeDrink Labs - Teste Rápido para Bebidas</title>
    <style>
        /* Reset e Base */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* Variáveis CSS */
        :root {
            --health-green: #7CB342;
            --health-green-light: #9CCC65;
            --health-green-dark: #558B2F;
            --health-blue: #1E88E5;
            --health-blue-light: #42A5F5;
            --health-teal: #26A69A;
            --health-teal-light: #4DB6AC;
            --navy-blue: #2C3E50;
            --navy-blue-dark: #1A252F;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        /* Header */
        .header {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e0e0e0;
            z-index: 1000;
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2rem;
        }

        .logo {
            height: 50px;
            width: auto;
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--navy-blue);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--health-green);
            transition: width 0.3s ease;
        }

        .nav-link:hover {
            color: var(--health-green);
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--health-green);
            color: white;
        }

        .btn-primary:hover {
            background: var(--health-green-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
        }

        .btn-secondary {
            background: white;
            color: var(--navy-blue);
            border: 2px solid var(--navy-blue);
        }

        .btn-secondary:hover {
            background: var(--navy-blue);
            color: white;
            transform: translateY(-2px);
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        /* Hero Section */
        .hero {
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-video-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -3;
            filter: blur(1px);
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(245, 247, 250, 0.85) 0%, rgba(232, 245, 233, 0.85) 100%);
            z-index: -2;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .hero-text {
            animation: fadeInUp 0.8s ease;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            color: var(--navy-blue);
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }

        .highlight {
            color: var(--health-green);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: #546e7a;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .hero-stats {
            display: flex;
            gap: 3rem;
        }

        .stat {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            color: var(--health-green);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--navy-blue);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #78909c;
        }

        .hero-image {
            animation: fadeInRight 0.8s ease;
        }

        .product-image {
            width: 100%;
            height: auto;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.05);
        }

        /* Alert Section */
        .alert-section {
            padding: 3rem 0;
            background: #fff3e0;
        }

        .alert-box {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            border-left: 4px solid #ff9800;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .alert-icon {
            width: 48px;
            height: 48px;
            color: #ff9800;
            flex-shrink: 0;
        }

        .alert-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 0.5rem;
        }

        .alert-text {
            font-size: 1rem;
            color: #546e7a;
            line-height: 1.6;
        }

        /* Section Styles */
        .section {
            padding: 5rem 0;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--navy-blue);
            text-align: center;
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #78909c;
            text-align: center;
            margin-bottom: 3rem;
        }

        /* Benefits Section */
        .benefits-section {
            background: white;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .benefit-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .benefit-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
            border-color: var(--health-green);
        }

        .benefit-icon-wrapper {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--health-green), var(--health-green-light));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: transform 0.3s ease;
        }

        .benefit-card:hover .benefit-icon-wrapper {
            transform: rotate(360deg);
        }

        .benefit-icon {
            width: 40px;
            height: 40px;
            color: white;
        }

        .benefit-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 0.75rem;
        }

        .benefit-description {
            font-size: 1rem;
            color: #546e7a;
            line-height: 1.6;
        }

        .benefits-image-wrapper {
            text-align: center;
            margin-top: 3rem;
        }

        .benefits-image {
            max-width: 600px;
            width: 100%;
            height: auto;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
        }

        /* How It Works Section */
        .how-it-works-section {
            background: linear-gradient(135deg, #e8f5e9 0%, #f5f7fa 100%);
        }

        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .step-card {
            background: white;
            padding: 2.5rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .step-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--health-green), var(--health-green-light));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .step-card:hover::before {
            transform: scaleX(1);
        }

        .step-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--health-green), var(--health-green-light));
            color: white;
            font-size: 2rem;
            font-weight: 700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .step-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 1rem;
        }

        .step-description {
            font-size: 1rem;
            color: #546e7a;
            line-height: 1.6;
        }

        /* Pricing Section */
        .pricing-section {
            background: white;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1100px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 16px;
            padding: 2.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
            border-color: var(--health-green);
        }

        .pricing-card.featured {
            border-color: var(--health-green);
            border-width: 3px;
            box-shadow: 0 8px 24px rgba(124, 179, 66, 0.2);
        }

        .featured-badge {
            position: absolute;
            top: -12px;
            right: 20px;
            background: var(--health-green);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .pricing-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #f5f5f5;
        }

        .pricing-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 1rem;
        }

        .pricing-price {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            gap: 0.25rem;
        }

        .currency {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--health-green);
            margin-top: 0.5rem;
        }

        .amount {
            font-size: 4rem;
            font-weight: 800;
            color: var(--navy-blue);
            line-height: 1;
        }

        .period {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--health-green);
            margin-top: 0.5rem;
        }

        .pricing-features {
            list-style: none;
            padding: 0;
            margin: 0 0 2rem 0;
        }

        .pricing-feature {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 0;
            color: #546e7a;
            font-size: 1rem;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            color: var(--health-green);
            flex-shrink: 0;
        }

        .pricing-button {
            width: 100%;
            background: var(--navy-blue);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pricing-button:hover {
            background: var(--navy-blue-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
        }

        .featured-button {
            background: var(--health-green);
        }

        .featured-button:hover {
            background: var(--health-green-dark);
            box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
        }

        /* Testimonials Section */
        .testimonials-section {
            background: white;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .testimonial-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
            border-color: var(--health-green);
        }

        .testimonial-card.featured {
            border-color: var(--health-green);
            border-width: 3px;
            box-shadow: 0 8px 24px rgba(124, 179, 66, 0.2);
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f5e9 100%);
        }

        .testimonial-video {
            width: 100%;
            height: 250px;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .testimonial-video:hover {
            transform: scale(1.02);
        }

        .testimonial-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, var(--health-green), var(--health-green-light));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
        }

        .testimonial-text {
            font-size: 1.1rem;
            color: #546e7a;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-style: italic;
            position: relative;
        }

        .testimonial-text::before {
            content: '"';
            font-size: 4rem;
            color: var(--health-green);
            position: absolute;
            top: -20px;
            left: -10px;
            font-family: serif;
            opacity: 0.3;
        }

        .testimonial-author {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--navy-blue);
            margin-bottom: 0.25rem;
        }

        .testimonial-role {
            font-size: 0.9rem;
            color: #78909c;
        }

        .testimonial-stars {
            display: flex;
            justify-content: center;
            gap: 0.25rem;
            margin-bottom: 1rem;
        }

        .star {
            width: 20px;
            height: 20px;
            color: #ffc107;
        }

        .featured-badge-testimonial {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--health-green);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Contact Section */
        .contact-section {
            background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        .contact-info {
            padding-right: 2rem;
        }

        .contact-description {
            font-size: 1.1rem;
            color: #546e7a;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .contact-methods {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .contact-method:hover {
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

        .contact-icon {
            width: 32px;
            height: 32px;
            color: var(--health-green);
            flex-shrink: 0;
        }

        .contact-label {
            font-size: 0.85rem;
            color: #78909c;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .contact-value {
            font-size: 1.1rem;
            color: var(--navy-blue);
            font-weight: 600;
        }

        .contact-form-wrapper {
            background: white;
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .contact-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--navy-blue);
        }

        .form-input,
        .form-textarea {
            padding: 0.875rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--health-green);
            box-shadow: 0 0 0 3px rgba(124, 179, 66, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-submit {
            background: var(--health-green);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .form-submit:hover {
            background: var(--health-green-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
        }

        /* Footer */
        .footer {
            background: var(--navy-blue);
            color: white;
            padding: 3rem 0 1.5rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 3rem;
            margin-bottom: 2rem;
        }

        .footer-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .footer-logo {
            height: 50px;
            width: auto;
            filter: brightness(0) invert(1);
        }

        .footer-text {
            color: #b0bec5;
            line-height: 1.6;
        }

        .footer-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .footer-links a {
            color: #b0bec5;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--health-green-light);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #90a4ae;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
                gap: 3rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-wrap: wrap;
            }

            .nav {
                display: none;
            }

            .logo {
                height: 35px;
            }

            .hero {
                padding: 3rem 0;
                min-height: 80vh;
            }

            .hero-title {
                font-size: 1.8rem;
                line-height: 1.3;
                margin-bottom: 1rem;
            }

            .hero-subtitle {
                font-size: 0.95rem;
                margin-bottom: 1.5rem;
            }

            .hero-stats {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 0.75rem;
                justify-content: center;
                max-width: 280px;
                margin: 0 auto;
            }

            .stat {
                flex-direction: column;
                text-align: center;
                gap: 0.25rem;
                padding: 0.75rem 0.5rem;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 8px;
                backdrop-filter: blur(10px);
            }

            .stat-icon {
                width: 24px;
                height: 24px;
                margin: 0 auto;
            }

            .stat-value {
                font-size: 1.2rem;
            }

            .stat-label {
                font-size: 0.75rem;
            }

            .hero-buttons {
                flex-direction: column;
                gap: 0.75rem;
            }

            .btn {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }

            .btn-lg {
                padding: 0.75rem 1.25rem;
                font-size: 0.95rem;
            }

            .section {
                padding: 3rem 0;
            }

            .section-title {
                font-size: 1.75rem;
                margin-bottom: 0.75rem;
            }

            .section-subtitle {
                font-size: 1rem;
                margin-bottom: 2rem;
            }

            .alert-section {
                padding: 2rem 0;
            }

            .alert-box {
                flex-direction: column;
                gap: 1rem;
                padding: 1.5rem;
            }

            .alert-icon {
                width: 28px;
                height: 28px;
                align-self: flex-start;
            }

            .alert-title {
                font-size: 1.2rem;
            }

            .alert-text {
                font-size: 0.9rem;
            }

            .benefit-icon-wrapper {
                width: 50px;
                height: 50px;
                margin-bottom: 1rem;
            }

            .benefit-icon {
                width: 24px;
                height: 24px;
            }

            .benefit-title {
                font-size: 1.05rem;
                margin-bottom: 0.5rem;
            }

            .benefit-description {
                font-size: 0.9rem;
            }

            .benefit-card {
                padding: 1.5rem;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .benefits-image-wrapper {
                margin-top: 2rem;
            }

            .benefits-image {
                max-width: 100%;
                border-radius: 12px;
            }

            .step-number {
                width: 40px;
                height: 40px;
                font-size: 1.4rem;
                margin-bottom: 1rem;
            }

            .step-title {
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .step-description {
                font-size: 0.9rem;
            }

            .step-card {
                padding: 1.75rem;
            }

            .steps-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .pricing-card {
                padding: 1.75rem;
            }

            .pricing-title {
                font-size: 1.2rem;
            }

            .amount {
                font-size: 3rem;
            }

            .currency, .period {
                font-size: 1.2rem;
            }

            .pricing-feature {
                font-size: 0.9rem;
                padding: 0.5rem 0;
            }

            .feature-icon {
                width: 16px;
                height: 16px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .testimonial-avatar {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .testimonial-text {
                font-size: 0.95rem;
                margin-bottom: 1rem;
            }

            .testimonial-author {
                font-size: 0.95rem;
            }

            .testimonial-role {
                font-size: 0.8rem;
            }

            .testimonial-card {
                padding: 1.5rem;
            }

            .star {
                width: 16px;
                height: 16px;
            }

            .contact-icon {
                width: 24px;
                height: 24px;
            }

            .contact-label {
                font-size: 0.75rem;
            }

            .contact-value {
                font-size: 0.95rem;
            }

            .contact-method {
                padding: 1.25rem;
            }

            .contact-info {
                padding-right: 0;
            }

            .contact-form-wrapper {
                padding: 1.75rem;
            }

            .form-input,
            .form-textarea {
                padding: 0.7rem;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 0.75rem;
            }

            .logo {
                height: 30px;
            }

            .btn {
                padding: 0.65rem 0.9rem;
                font-size: 0.85rem;
            }

            .btn-lg {
                padding: 0.7rem 1rem;
                font-size: 0.9rem;
            }

            .hero {
                padding: 2rem 0;
                min-height: 70vh;
            }

            .hero-video-background {
                filter: blur(0.5px);
            }

            .hero-title {
                font-size: 1.4rem;
                line-height: 1.3;
                margin-bottom: 0.75rem;
            }

            .hero-subtitle {
                font-size: 0.85rem;
                margin-bottom: 1.25rem;
            }

            .hero-stats {
                gap: 0.5rem;
                margin-bottom: 2rem;
                max-width: 250px;
            }

            .stat {
                padding: 0.5rem 0.25rem;
            }

            .stat-icon {
                width: 20px;
                height: 20px;
            }

            .stat-value {
                font-size: 1.1rem;
            }

            .stat-label {
                font-size: 0.7rem;
            }

            .section {
                padding: 2.5rem 0;
            }

            .section-title {
                font-size: 1.5rem;
                margin-bottom: 0.5rem;
            }

            .section-subtitle {
                font-size: 0.9rem;
                margin-bottom: 1.5rem;
            }

            .alert-section {
                padding: 1.5rem 0;
            }

            .alert-box {
                padding: 1.25rem;
                gap: 0.75rem;
            }

            .alert-icon {
                width: 24px;
                height: 24px;
            }

            .alert-title {
                font-size: 1.1rem;
                margin-bottom: 0.25rem;
            }

            .alert-text {
                font-size: 0.85rem;
            }

            .benefit-icon-wrapper {
                width: 40px;
                height: 40px;
                margin-bottom: 0.75rem;
            }

            .benefit-icon {
                width: 20px;
                height: 20px;
            }

            .benefit-title {
                font-size: 1rem;
                margin-bottom: 0.4rem;
            }

            .benefit-description {
                font-size: 0.85rem;
            }

            .benefit-card {
                padding: 1.25rem;
            }

            .benefits-grid {
                gap: 1.25rem;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 1.2rem;
                margin-bottom: 0.75rem;
            }

            .step-title {
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
            }

            .step-description {
                font-size: 0.85rem;
            }

            .step-card {
                padding: 1.5rem;
            }

            .steps-grid {
                gap: 1.25rem;
            }

            .pricing-grid {
                gap: 1.25rem;
            }

            .pricing-card {
                padding: 1.5rem;
            }

            .pricing-title {
                font-size: 1.1rem;
            }

            .amount {
                font-size: 2.5rem;
            }

            .currency, .period {
                font-size: 1.1rem;
            }

            .pricing-feature {
                font-size: 0.85rem;
                padding: 0.4rem 0;
            }

            .feature-icon {
                width: 14px;
                height: 14px;
            }

            .pricing-button {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .testimonials-grid {
                gap: 1.25rem;
            }

            .testimonial-avatar {
                width: 45px;
                height: 45px;
                font-size: 1.1rem;
                margin-bottom: 0.5rem;
            }

            .testimonial-text {
                font-size: 0.85rem;
                margin-bottom: 0.75rem;
            }

            .testimonial-author {
                font-size: 0.9rem;
            }

            .testimonial-role {
                font-size: 0.75rem;
            }

            .testimonial-card {
                padding: 1.25rem;
            }

            .star {
                width: 14px;
                height: 14px;
            }

            .contact-icon {
                width: 20px;
                height: 20px;
            }

            .contact-label {
                font-size: 0.7rem;
            }

            .contact-value {
                font-size: 0.85rem;
            }

            .contact-method {
                padding: 1rem;
            }

            .contact-form-wrapper {
                padding: 1.25rem;
            }

            .form-label {
                font-size: 0.85rem;
            }

            .form-input,
            .form-textarea {
                padding: 0.6rem;
                font-size: 0.85rem;
            }

            .form-submit {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .footer {
                padding: 2rem 0 1.25rem;
            }

            .footer-logo {
                height: 35px;
            }

            .footer-text {
                font-size: 0.85rem;
            }

            .footer-title {
                font-size: 0.95rem;
            }

            .footer-links {
                font-size: 0.85rem;
            }
        }

        /* Media query para dispositivos muito pequenos */
        @media (max-width: 320px) {
            .container {
                padding: 0 0.5rem;
            }

            .logo {
                height: 28px;
            }

            .hero-title {
                font-size: 1.25rem;
                line-height: 1.2;
            }

            .hero-subtitle {
                font-size: 0.8rem;
            }

            .hero-stats {
                max-width: 220px;
                gap: 0.4rem;
            }

            .stat {
                padding: 0.4rem 0.2rem;
            }

            .stat-icon {
                width: 18px;
                height: 18px;
            }

            .stat-value {
                font-size: 1rem;
            }

            .stat-label {
                font-size: 0.65rem;
            }

            .section-title {
                font-size: 1.3rem;
            }

            .section-subtitle {
                font-size: 0.85rem;
            }

            .benefit-icon-wrapper {
                width: 35px;
                height: 35px;
            }

            .benefit-icon {
                width: 18px;
                height: 18px;
            }

            .benefit-title {
                font-size: 0.95rem;
            }

            .benefit-description {
                font-size: 0.8rem;
            }

            .step-number {
                width: 30px;
                height: 30px;
                font-size: 1rem;
            }

            .step-title {
                font-size: 1rem;
            }

            .step-description {
                font-size: 0.8rem;
            }

            .amount {
                font-size: 2.2rem;
            }

            .testimonial-avatar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .contact-icon {
                width: 18px;
                height: 18px;
            }
        }

        /* Melhorias para o vídeo de fundo */
        @media (max-width: 768px) {
            .hero-video-background {
                filter: blur(0.5px);
            }

            .hero-overlay {
                background: linear-gradient(135deg, rgba(245, 247, 250, 0.9) 0%, rgba(232, 245, 233, 0.9) 100%);
            }
        }
    </style>
</head>
<body>
    <!-- Script de Integração Checkout -->
    <script>
        // Configuração do Pagloop (Gateway de Pagamento)
        const PAGLOOP_CONFIG = {
            publicKey: 'pk_SrSHn_wcqgI-ED8wRthOgZ-QEuU4Hr1-z4MS2fN7AwVC7WNE',
            environment: 'production'
        };

        // Links de checkout do Zedy - Configurados e prontos para uso!
        const ZEDY_CHECKOUT_LINKS = {
            '20-units': 'https://zedy.com.br/api/public/shopify?product=1498917457139&store=14989',
            '40-units': 'https://zedy.com.br/api/public/shopify?product=1498917896952&store=14989',
            '100-units': 'https://zedy.com.br/api/public/shopify?product=1498981865111&store=14989',
            '500-units': 'https://zedy.com.br/api/public/shopify?product=1498914285283&store=14989',
            '1000-units': 'https://zedy.com.br/api/public/shopify?product=1498944824789&store=14989'
        };

        // Produtos disponíveis
        const PRODUCTS = {
            '20-units': {
                id: 'kit-20-unidades',
                sku: 'SDL-20',
                name: 'Kit 20 Unidades',
                price: 2990, // preço em centavos (R$ 29,90)
                description: '20 testes para detecção de metanol em bebidas',
                quantity: 20,
                image: window.location.origin + '/IMG_1792.jpg'
            },
            '40-units': {
                id: 'kit-40-unidades',
                sku: 'SDL-40',
                name: 'Kit 40 Unidades',
                price: 5490, // preço em centavos (R$ 54,90)
                description: '40 testes para detecção de metanol em bebidas',
                quantity: 40,
                image: window.location.origin + '/IMG_1792.jpg'
            },
            '100-units': {
                id: 'kit-100-unidades',
                sku: 'SDL-100',
                name: 'Kit 100 Unidades',
                price: 12990, // preço em centavos (R$ 129,90)
                description: '100 testes para detecção de metanol em bebidas',
                quantity: 100,
                image: window.location.origin + '/IMG_1792.jpg'
            },
            '500-units': {
                id: 'kit-500-unidades',
                sku: 'SDL-500',
                name: 'Caixa com 500 Unidades',
                price: 39900, // preço em centavos (R$ 399,00)
                description: '500 testes para detecção de metanol em bebidas',
                quantity: 500,
                image: window.location.origin + '/IMG_1792.jpg'
            },
            '1000-units': {
                id: 'kit-1000-unidades',
                sku: 'SDL-1000',
                name: 'Caixa com 1000 Unidades',
                price: 69900, // preço em centavos (R$ 699,00)
                description: '1000 testes para detecção de metanol em bebidas',
                quantity: 1000,
                image: window.location.origin + '/IMG_1792.jpg'
            }
        };

        // Função para redirecionar para o checkout
        function redirectToCheckout(productKey) {
            const product = PRODUCTS[productKey];

            if (!product) {
                console.error('Produto não encontrado:', productKey);
                alert('Produto não encontrado. Por favor, tente novamente.');
                return;
            }

            // Obter link do checkout Zedy
            const zedyLink = ZEDY_CHECKOUT_LINKS[productKey];

            if (!zedyLink) {
                console.error('Link de checkout não encontrado para:', productKey);
                alert('Erro ao processar checkout. Por favor, tente novamente.');
                return;
            }

            // Analytics - Rastrear início do checkout
            trackCheckoutEvent(product);

            // Log para debug
            console.log('🛒 Iniciando checkout:', {
                produto: product.name,
                preco: 'R$ ' + (product.price / 100).toFixed(2).replace('.', ','),
                url: zedyLink
            });

            // Redirecionar para o checkout do Zedy
            window.location.href = zedyLink;
        }

        // Função para rastrear eventos de checkout (Google Analytics, Facebook Pixel, etc.)
        function trackCheckoutEvent(product) {
            // Google Analytics 4
            if (typeof gtag !== 'undefined') {
                gtag('event', 'begin_checkout', {
                    currency: 'BRL',
                    value: product.price / 100,
                    items: [{
                        item_id: product.id,
                        item_name: product.name,
                        item_category: 'Teste de Metanol',
                        price: product.price / 100,
                        quantity: 1
                    }]
                });
            }

            // Facebook Pixel
            if (typeof fbq !== 'undefined') {
                fbq('track', 'InitiateCheckout', {
                    content_name: product.name,
                    content_ids: [product.id],
                    content_type: 'product',
                    value: product.price / 100,
                    currency: 'BRL'
                });
            }
        }

        // Função para configurar os botões de checkout
        function setupCheckoutButtons() {
            // Botões dos cards de preços
            const pricingButtons = document.querySelectorAll('.pricing-button');
            const productKeys = ['20-units', '40-units', '100-units', '500-units', '1000-units'];

            pricingButtons.forEach((button, index) => {
                const productKey = productKeys[index];

                if (productKey) {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        redirectToCheckout(productKey);
                    });
                }
            });

            // Botões "Comprar Agora" no header e hero - scroll suave para preços
            const buyNowButtons = document.querySelectorAll('a[href="#precos"]');
            buyNowButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    const precosSection = document.getElementById('precos');
                    if (precosSection) {
                        precosSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Inicializar quando o DOM estiver carregado
        document.addEventListener('DOMContentLoaded', setupCheckoutButtons);

        // Log de inicialização (remover em produção)
        console.log('Sistema de checkout inicializado:', {
            gateway: 'Pagloop',
            checkout: 'Zedy',
            products: Object.keys(PRODUCTS).length
        });
    </script>
    <div class="landing-page">
        <!-- Header/Navbar -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <img src="1111.png" alt="SafeDrink Labs" class="logo">
                    <nav class="nav">
                        <a href="#produto" class="nav-link">Produto</a>
                        <a href="#beneficios" class="nav-link">Benefícios</a>
                        <a href="#como-funciona" class="nav-link">Como Funciona</a>
                        <a href="#precos" class="nav-link">Preços</a>
                        <a href="#contato" class="nav-link">Contato</a>
                    </nav>
                    <a href="#precos" class="btn btn-primary">Comprar Agora</a>
                </div>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <video class="hero-video-background" autoplay muted loop playsinline>
                <source src="123.mp4" type="video/mp4">
                Seu navegador não suporta vídeos HTML5.
            </video>
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title">
                            Proteja-se contra o <span class="highlight">Metanol</span>
                        </h1>
                        <p class="hero-subtitle">
                            Teste rápido e confiável para detectar contaminação por metanol em bebidas alcoólicas.
                            Resultado em apenas 15 segundos com 99% de precisão.
                        </p>
                        <div class="hero-buttons">
                            <a href="#precos" class="btn btn-primary btn-lg">
                                <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Comprar Agora
                            </a>
                            <a href="#beneficios" class="btn btn-secondary btn-lg">
                                Saiba Mais
                            </a>
                        </div>
                        <div class="hero-stats">
                            <div class="stat">
                                <svg class="stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                <div>
                                    <div class="stat-value">15s</div>
                                    <div class="stat-label">Resultado Rápido</div>
                                </div>
                            </div>
                            <div class="stat">
                                <svg class="stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <div class="stat-value">99%</div>
                                    <div class="stat-label">Precisão</div>
                                </div>
                            </div>
                            <div class="stat">
                                <svg class="stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <div class="stat-value">30 dias</div>
                                    <div class="stat-label">Garantia</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="IMG_1792.jpg" alt="Produtos SafeDrink Labs" class="product-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Alerta Section -->
        <section class="alert-section">
            <div class="container">
                <div class="alert-box">
                    <svg class="alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div class="alert-content">
                        <h3 class="alert-title">Por que você precisa do SafeDrink Labs?</h3>
                        <p class="alert-text">
                            O metanol é uma substância tóxica que pode causar sérios danos à saúde, incluindo cegueira e até morte.
                            Infelizmente, bebidas adulteradas com metanol são uma realidade em muitos estabelecimentos.
                            Proteja-se e sua família com nosso teste rápido e confiável.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Benefícios Section -->
        <section id="beneficios" class="section benefits-section">
            <div class="container">
                <h2 class="section-title">Por que escolher SafeDrink Labs?</h2>
                <p class="section-subtitle">
                    Tecnologia de ponta para sua segurança e tranquilidade
                </p>
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <div class="benefit-icon-wrapper">
                            <svg class="benefit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                        </div>
                        <h3 class="benefit-title">Resultado Rápido</h3>
                        <p class="benefit-description">
                            Detecção em apenas 15 segundos. Não perca tempo e tenha a resposta imediatamente.
                        </p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon-wrapper">
                            <svg class="benefit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="benefit-title">99% de Precisão</h3>
                        <p class="benefit-description">
                            Certificado em laboratórios especializados. Confiança total nos resultados.
                        </p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon-wrapper">
                            <svg class="benefit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <h3 class="benefit-title">Fácil de Usar</h3>
                        <p class="benefit-description">
                            Instruções simples em português. Qualquer pessoa pode realizar o teste.
                        </p>
                    </div>
                    <div class="benefit-card">
                        <div class="benefit-icon-wrapper">
                            <svg class="benefit-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="benefit-title">Garantia de 30 Dias</h3>
                        <p class="benefit-description">
                            Todos os produtos com garantia completa. Sua satisfação é nossa prioridade.
                        </p>
                    </div>
                </div>
                <div class="benefits-image-wrapper">
                    <img src="generated-image.png" alt="Benefícios SafeDrink Labs" class="benefits-image">
                </div>
            </div>
        </section>

        <!-- Como Funciona Section -->
        <section id="como-funciona" class="section how-it-works-section">
            <div class="container">
                <h2 class="section-title">Como Funciona</h2>
                <p class="section-subtitle">
                    Simples, rápido e eficaz em 3 passos
                </p>
                <div class="steps-grid">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h3 class="step-title">Colete a Amostra</h3>
                        <p class="step-description">
                            Coloque uma pequena quantidade da bebida no recipiente de teste fornecido.
                        </p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h3 class="step-title">Aguarde 15 Segundos</h3>
                        <p class="step-description">
                            O teste reage com a bebida e detecta a presença de metanol automaticamente.
                        </p>
                    </div>
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h3 class="step-title">Leia o Resultado</h3>
                        <p class="step-description">
                            Verifique a mudança de cor no indicador. Instruções claras em português.
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Preços Section -->
        <section id="precos" class="section pricing-section">
            <div class="container">
                <h2 class="section-title">Escolha seu Kit</h2>
                <p class="section-subtitle">
                    Planos para todas as necessidades
                </p>
                <div class="pricing-grid">
                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">20 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">29</span>
                                <span class="period">,90</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>20 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                        </ul>
                        <button class="pricing-button">Comprar Agora</button>
                    </div>

                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">40 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">54</span>
                                <span class="period">,90</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>40 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                        </ul>
                        <button class="pricing-button">Comprar Agora</button>
                    </div>

                    <div class="pricing-card featured">
                        <div class="featured-badge">Mais Popular</div>
                        <div class="pricing-header">
                            <h3 class="pricing-title">100 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">129</span>
                                <span class="period">,90</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>100 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Frete grátis</span>
                            </li>
                        </ul>
                        <button class="pricing-button featured-button">Comprar Agora</button>
                    </div>

                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">Caixa com 500 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">399</span>
                                <span class="period">,00</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>500 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Frete grátis</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Suporte prioritário</span>
                            </li>
                        </ul>
                        <button class="pricing-button">Comprar Agora</button>
                    </div>

                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">Caixa com 1000 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">699</span>
                                <span class="period">,00</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>1000 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Frete grátis</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Suporte prioritário</span>
                            </li>
                        </ul>
                        <button class="pricing-button">Comprar Agora</button>
                    </div>

                    <div class="pricing-card">
                        <div class="pricing-header">
                            <h3 class="pricing-title">Caixa com 5000 Unidades</h3>
                            <div class="pricing-price">
                                <span class="currency">R$</span>
                                <span class="amount">2.995</span>
                                <span class="period">,00</span>
                            </div>
                        </div>
                        <ul class="pricing-features">
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>5000 testes inclusos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Resultado em 15 segundos</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Instruções em português</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Garantia de 30 dias</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Frete grátis</span>
                            </li>
                            <li class="pricing-feature">
                                <svg class="feature-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Suporte prioritário</span>
                            </li>
                        </ul>
                        <button class="pricing-button">Comprar Agora</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="section testimonials-section">
            <div class="container">
                <h2 class="section-title">O que nossos clientes dizem</h2>
                <p class="section-subtitle">
                    Depoimentos reais de pessoas que confiam na SafeDrink Labs
                </p>
                <div class="testimonials-grid">
                    <div class="testimonial-card">
                        <div class="testimonial-avatar">M</div>
                        <div class="testimonial-stars">
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                        <p class="testimonial-text">
                            Incrível! Testei uma bebida suspeita em um bar e o resultado foi positivo para metanol. O SafeDrink Labs literalmente salvou minha vida. Agora sempre carrego comigo.
                        </p>
                        <div class="testimonial-author">Maria Silva</div>
                        <div class="testimonial-role">Empresária, São Paulo</div>
                    </div>

                    <div class="testimonial-card featured">
                        <div class="featured-badge-testimonial">Cliente Verificado</div>
                        <video class="testimonial-video" controls poster="">
                            <source src="1111.mp4" type="video/mp4">
                            Seu navegador não suporta vídeos HTML5.
                        </video>
                        <div class="testimonial-stars">
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                        <p class="testimonial-text">
                            Veja como é fácil usar o SafeDrink Labs! Em apenas 15 segundos consegui detectar metanol na bebida. A tranquilidade que isso me dá é impagável.
                        </p>
                        <div class="testimonial-author">João Santos</div>
                        <div class="testimonial-role">Engenheiro, Rio de Janeiro</div>
                    </div>

                    <div class="testimonial-card">
                        <div class="testimonial-avatar">A</div>
                        <div class="testimonial-stars">
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="star" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                        <p class="testimonial-text">
                            Como médica, recomendo o SafeDrink Labs para todos os meus pacientes. É uma ferramenta essencial de prevenção. A precisão é impressionante!
                        </p>
                        <div class="testimonial-author">Dra. Ana Costa</div>
                        <div class="testimonial-role">Médica Toxicologista, Brasília</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contato Section -->
        <section id="contato" class="section contact-section">
            <div class="container">
                <div class="contact-grid">
                    <div class="contact-info">
                        <h2 class="section-title">Entre em Contato</h2>
                        <p class="contact-description">
                            Tem dúvidas? Nossa equipe está pronta para ajudar você a escolher o melhor kit para suas necessidades.
                        </p>
                        <div class="contact-methods">
                            <div class="contact-method">
                                <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                <div>
                                    <div class="contact-label">Telefone</div>
                                    <div class="contact-value">(11) 98765-4321</div>
                                </div>
                            </div>
                            <div class="contact-method">
                                <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <div>
                                    <div class="contact-label">Email</div>
                                    <div class="contact-value"><EMAIL></div>
                                </div>
                            </div>
                            <div class="contact-method">
                                <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <div>
                                    <div class="contact-label">Endereço</div>
                                    <div class="contact-value">São Paulo, SP - Brasil</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="contact-form-wrapper">
                        <form id="contactForm" class="contact-form">
                            <div class="form-group">
                                <label for="nome" class="form-label">Nome</label>
                                <input
                                    type="text"
                                    id="nome"
                                    name="nome"
                                    class="form-input"
                                    required
                                >
                            </div>
                            <div class="form-group">
                                <label for="email" class="form-label">Email</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    class="form-input"
                                    required
                                >
                            </div>
                            <div class="form-group">
                                <label for="telefone" class="form-label">Telefone</label>
                                <input
                                    type="tel"
                                    id="telefone"
                                    name="telefone"
                                    class="form-input"
                                >
                            </div>
                            <div class="form-group">
                                <label for="mensagem" class="form-label">Mensagem</label>
                                <textarea
                                    id="mensagem"
                                    name="mensagem"
                                    class="form-textarea"
                                    rows="4"
                                    required
                                ></textarea>
                            </div>
                            <button type="submit" class="form-submit">
                                Enviar Mensagem
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <img src="1111.png" alt="SafeDrink Labs" class="footer-logo">
                        <p class="footer-text">
                            Protegendo vidas através da tecnologia e inovação.
                        </p>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-title">Links Rápidos</h4>
                        <ul class="footer-links">
                            <li><a href="#produto">Produto</a></li>
                            <li><a href="#beneficios">Benefícios</a></li>
                            <li><a href="#como-funciona">Como Funciona</a></li>
                            <li><a href="#precos">Preços</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4 class="footer-title">Suporte</h4>
                        <ul class="footer-links">
                            <li><a href="#contato">Contato</a></li>
                            <li><a href="#">FAQ</a></li>
                            <li><a href="#">Política de Privacidade</a></li>
                            <li><a href="#">Termos de Uso</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2025 SafeDrink Labs. Todos os direitos reservados.</p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Garantir que o vídeo seja reproduzido
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('.hero-video-background');
            if (video) {
                video.play().catch(function(error) {
                    console.log('Autoplay foi bloqueado:', error);
                });
            }
        });

        // Formulário de contato
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simular envio do formulário
            alert('Obrigado pelo contato! Entraremos em contato em breve.');

            // Limpar formulário
            this.reset();
        });

        // Smooth scroll para links de navegação
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Adicionar classe ativa ao header quando rolar a página
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
