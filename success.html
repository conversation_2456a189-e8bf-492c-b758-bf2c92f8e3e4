<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compra Realizada com Sucesso - SafeDrink Labs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #e8f5e9 0%, #f5f7fa 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }

        .success-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 100%;
            padding: 3rem;
            text-align: center;
            animation: fadeInUp 0.6s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #7CB342, #9CCC65);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            animation: scaleIn 0.5s ease 0.3s both;
        }

        @keyframes scaleIn {
            from {
                transform: scale(0);
            }
            to {
                transform: scale(1);
            }
        }

        .success-icon svg {
            width: 60px;
            height: 60px;
            color: white;
        }

        .success-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 1rem;
        }

        .success-message {
            font-size: 1.1rem;
            color: #546e7a;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .order-details {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }

        .order-detail {
            display: flex;
            justify-content: space-between;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .order-detail:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #2C3E50;
        }

        .detail-value {
            color: #546e7a;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            margin: 0.5rem;
        }

        .btn-primary {
            background: #7CB342;
            color: white;
        }

        .btn-primary:hover {
            background: #558B2F;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
        }

        .btn-secondary {
            background: white;
            color: #2C3E50;
            border: 2px solid #2C3E50;
        }

        .btn-secondary:hover {
            background: #2C3E50;
            color: white;
            transform: translateY(-2px);
        }

        .info-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 2rem;
            text-align: left;
        }

        .info-box p {
            color: #546e7a;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .success-container {
                padding: 2rem;
            }

            .success-title {
                font-size: 1.5rem;
            }

            .success-message {
                font-size: 1rem;
            }

            .btn {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>

        <h1 class="success-title">Compra Realizada com Sucesso!</h1>
        <p class="success-message">
            Obrigado por escolher SafeDrink Labs! Seu pedido foi confirmado e você receberá um e-mail com os detalhes da compra e informações de rastreamento.
        </p>

        <div class="order-details" id="orderDetails">
            <div class="order-detail">
                <span class="detail-label">Número do Pedido:</span>
                <span class="detail-value" id="orderId">Processando...</span>
            </div>
            <div class="order-detail">
                <span class="detail-label">Produto:</span>
                <span class="detail-value" id="productName">-</span>
            </div>
            <div class="order-detail">
                <span class="detail-label">Valor:</span>
                <span class="detail-value" id="orderAmount">-</span>
            </div>
            <div class="order-detail">
                <span class="detail-label">Status:</span>
                <span class="detail-value" style="color: #7CB342; font-weight: 600;">Confirmado</span>
            </div>
        </div>

        <div>
            <a href="index.html" class="btn btn-primary">Voltar para a Página Inicial</a>
            <a href="mailto:<EMAIL>" class="btn btn-secondary">Falar com Suporte</a>
        </div>

        <div class="info-box">
            <p>
                <strong>📧 Próximos Passos:</strong><br>
                • Você receberá um e-mail de confirmação em breve<br>
                • Seu pedido será processado em até 24 horas<br>
                • O código de rastreamento será enviado por e-mail<br>
                • Prazo de entrega: 5-10 dias úteis
            </p>
        </div>
    </div>

    <script>
        // Extrair parâmetros da URL
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                orderId: params.get('order_id') || params.get('transaction_id'),
                productName: params.get('product_name'),
                amount: params.get('amount'),
                status: params.get('status')
            };
        }

        // Atualizar detalhes do pedido
        function updateOrderDetails() {
            const params = getUrlParams();
            
            if (params.orderId) {
                document.getElementById('orderId').textContent = params.orderId;
            }
            
            if (params.productName) {
                document.getElementById('productName').textContent = decodeURIComponent(params.productName);
            }
            
            if (params.amount) {
                const amount = parseFloat(params.amount) / 100;
                document.getElementById('orderAmount').textContent = `R$ ${amount.toFixed(2).replace('.', ',')}`;
            }

            // Enviar evento de conversão (Google Analytics, Facebook Pixel, etc.)
            if (typeof gtag !== 'undefined' && params.amount) {
                gtag('event', 'purchase', {
                    transaction_id: params.orderId,
                    value: parseFloat(params.amount) / 100,
                    currency: 'BRL',
                    items: [{
                        item_name: params.productName,
                        price: parseFloat(params.amount) / 100,
                        quantity: 1
                    }]
                });
            }
        }

        // Executar quando a página carregar
        document.addEventListener('DOMContentLoaded', updateOrderDetails);
    </script>
</body>
</html>

