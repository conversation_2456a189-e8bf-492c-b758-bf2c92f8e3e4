# Integração Checkout - SafeDrink Labs

## 📋 Visão Geral

Este documento descreve a integração entre a landing page SafeDrink Labs, o checkout Zedy e o gateway de pagamento Pagloop.

## 🔧 Configuração

### C<PERSON><PERSON><PERSON><PERSON> (Gateway de Pagamento)

- **Cha<PERSON> (Public Key):** `pk_SrSHn_wcqgI-ED8wRthOgZ-QEuU4Hr1-z4MS2fN7AwVC7WNE`
- **Chave <PERSON>ri<PERSON> (Private Key):** `sk_hgEg6RARGBXibr9LwSBg3nvxnOmfhRmCpYAOLDeWco0AxFgD`
- **Ambiente:** Production
- **Documentação:** https://app.pagloop.com/docs/intro/first-steps

### Configuração Zedy (Checkout)

- **URL do Checkout:** https://zedy.com.br
- **Email:** <EMAIL>
- **Senha:** Qwe121212.

## 🛍️ Produtos Configurados

| Produto | SKU | Preço | Descrição |
|---------|-----|-------|-----------|
| Kit 20 Unidades | SDL-20 | R$ 29,90 | 20 testes para detecção de metanol |
| Kit 40 Unidades | SDL-40 | R$ 54,90 | 40 testes para detecção de metanol |
| Kit 100 Unidades | SDL-100 | R$ 129,90 | 100 testes para detecção de metanol |
| Caixa 500 Unidades | SDL-500 | R$ 399,00 | 500 testes para detecção de metanol |
| Caixa 1000 Unidades | SDL-1000 | R$ 699,00 | 1000 testes para detecção de metanol |

## 🔄 Fluxo de Compra

```
1. Cliente clica em "Comprar Agora" na landing page
   ↓
2. JavaScript prepara dados do produto + gateway Pagloop
   ↓
3. Redireciona para Zedy com parâmetros:
   - Dados do produto
   - Chave pública do Pagloop
   - URLs de retorno (success/cancel)
   ↓
4. Cliente preenche dados no checkout Zedy
   ↓
5. Zedy processa pagamento via Pagloop
   ↓
6. Cliente é redirecionado:
   - Sucesso → success.html
   - Cancelamento → cancel.html
```

## 📝 Parâmetros Enviados ao Checkout

Quando o cliente clica em "Comprar Agora", os seguintes parâmetros são enviados para o Zedy:

```javascript
{
  // Gateway de Pagamento
  gateway: 'pagloop',
  gateway_public_key: 'pk_SrSHn_wcqgI-ED8wRthOgZ-QEuU4Hr1-z4MS2fN7AwVC7WNE',
  
  // Dados do Produto
  product_id: 'kit-20-unidades',
  product_sku: 'SDL-20',
  product_name: 'Kit 20 Unidades',
  product_description: '20 testes para detecção de metanol em bebidas',
  product_image: 'https://seusite.com/IMG_1792.jpg',
  
  // Preço
  amount: 2990, // em centavos
  quantity: 1,
  currency: 'BRL',
  
  // URLs de Retorno
  success_url: 'https://seusite.com/success.html',
  cancel_url: 'https://seusite.com/cancel.html',
  
  // Metadados
  metadata: {
    product_id: 'kit-20-unidades',
    product_sku: 'SDL-20',
    source: 'landing-page-safedrinklabs',
    units: 20,
    timestamp: '2025-01-15T10:30:00Z'
  }
}
```

## ⚙️ Configuração no Zedy

### Passo 1: Login no Zedy
1. Acesse: https://zedy.com.br
2. Faça login com:
   - Email: <EMAIL>
   - Senha: Qwe121212.

### Passo 2: Configurar Gateway Pagloop
1. Vá em **Configurações** → **Gateways de Pagamento**
2. Adicione o **Pagloop** como gateway
3. Insira as credenciais:
   - Chave Pública: `pk_SrSHn_wcqgI-ED8wRthOgZ-QEuU4Hr1-z4MS2fN7AwVC7WNE`
   - Chave Privada: `sk_hgEg6RARGBXibr9LwSBg3nvxnOmfhRmCpYAOLDeWco0AxFgD`
4. Ative o gateway

### Passo 3: Cadastrar Produtos
Cadastre cada produto no Zedy com os seguintes dados:

**Kit 20 Unidades:**
- Nome: Kit 20 Unidades
- SKU: SDL-20
- Preço: R$ 29,90
- Descrição: 20 testes para detecção de metanol em bebidas

**Kit 40 Unidades:**
- Nome: Kit 40 Unidades
- SKU: SDL-40
- Preço: R$ 54,90
- Descrição: 40 testes para detecção de metanol em bebidas

**Kit 100 Unidades:**
- Nome: Kit 100 Unidades
- SKU: SDL-100
- Preço: R$ 129,90
- Descrição: 100 testes para detecção de metanol em bebidas

**Caixa 500 Unidades:**
- Nome: Caixa com 500 Unidades
- SKU: SDL-500
- Preço: R$ 399,00
- Descrição: 500 testes para detecção de metanol em bebidas

**Caixa 1000 Unidades:**
- Nome: Caixa com 1000 Unidades
- SKU: SDL-1000
- Preço: R$ 699,00
- Descrição: 1000 testes para detecção de metanol em bebidas

### Passo 4: Configurar URLs de Retorno
1. Vá em **Configurações** → **Checkout**
2. Configure as URLs de retorno:
   - URL de Sucesso: `https://seudominio.com/success.html`
   - URL de Cancelamento: `https://seudominio.com/cancel.html`

### Passo 5: Ativar Recebimento de Parâmetros
1. Certifique-se de que o Zedy está configurado para aceitar parâmetros via URL
2. Ative a opção de "Checkout Dinâmico" ou "API de Checkout"

## 📊 Rastreamento e Analytics

O sistema está configurado para rastrear eventos em:

### Google Analytics 4
- Evento: `begin_checkout`
- Dados enviados: produto, valor, moeda

### Facebook Pixel
- Evento: `InitiateCheckout`
- Dados enviados: nome do produto, ID, valor

## 🔒 Segurança

⚠️ **IMPORTANTE:**
- A chave privada (`sk_...`) **NUNCA** deve ser usada no frontend
- Ela está no código apenas como referência
- Configure-a apenas no backend do Zedy
- A landing page usa apenas a chave pública (`pk_...`)

## 🧪 Testes

### Testar o Fluxo Completo:
1. Abra a landing page: `index.html`
2. Clique em "Comprar Agora" em qualquer produto
3. Verifique se redireciona para o Zedy com os parâmetros corretos
4. Complete o checkout
5. Verifique se retorna para `success.html` ou `cancel.html`

### Verificar Parâmetros:
Abra o Console do navegador (F12) e veja os logs:
```
Sistema de checkout inicializado: {gateway: 'Pagloop', checkout: 'Zedy', products: 5}
Redirecionando para checkout: {...}
```

## 📞 Suporte

### Pagloop
- Documentação: https://app.pagloop.com/docs/intro/first-steps
- Integrações: https://app.pagloop.com/integrations

### Zedy
- Site: https://zedy.com.br
- Login: <EMAIL>

## 🚀 Próximos Passos

1. ✅ Fazer login no Zedy
2. ✅ Configurar gateway Pagloop no Zedy
3. ✅ Cadastrar os 5 produtos
4. ✅ Configurar URLs de retorno
5. ✅ Testar o fluxo completo
6. ✅ Ativar modo produção

## 📝 Notas Adicionais

- Os preços estão em centavos no código (2990 = R$ 29,90)
- As URLs de retorno são dinâmicas e se adaptam ao domínio
- O sistema suporta Google Analytics e Facebook Pixel
- Todos os produtos incluem imagem, descrição e metadados

---

**Última atualização:** 2025-01-15
**Versão:** 1.0

