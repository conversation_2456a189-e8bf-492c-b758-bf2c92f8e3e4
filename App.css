@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --color-input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  
  /* Health-themed colors */
  --health-green: #7CB342;
  --health-green-light: #9CCC65;
  --health-green-dark: #558B2F;
  --health-blue: #1E88E5;
  --health-blue-light: #42A5F5;
  --health-teal: #26A69A;
  --health-teal-light: #4DB6AC;
  --navy-blue: #2C3E50;
  --navy-blue-dark: #1A252F;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --color-input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  }
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Header */
.header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e0e0e0;
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  color: var(--navy-blue);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--health-green);
  transition: width 0.3s ease;
}

.nav-link:hover {
  color: var(--health-green);
}

.nav-link:hover::after {
  width: 100%;
}

.cta-button {
  background: var(--health-green);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: var(--health-green-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
}

/* Hero Section */
.hero {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-text {
  animation: fadeInUp 0.8s ease;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--navy-blue);
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.highlight {
  color: var(--health-green);
  position: relative;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: #546e7a;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.primary-button {
  background: var(--health-green);
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.primary-button:hover {
  background: var(--health-green-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(124, 179, 66, 0.4);
}

.secondary-button {
  background: white;
  color: var(--navy-blue);
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  border: 2px solid var(--navy-blue);
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background: var(--navy-blue);
  color: white;
  transform: translateY(-2px);
}

.button-icon {
  width: 20px;
  height: 20px;
}

.hero-stats {
  display: flex;
  gap: 3rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  color: var(--health-green);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--navy-blue);
}

.stat-label {
  font-size: 0.9rem;
  color: #78909c;
}

.hero-image {
  animation: fadeInRight 0.8s ease;
}

.product-image {
  width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

/* Alert Section */
.alert-section {
  padding: 3rem 0;
  background: #fff3e0;
}

.alert-box {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  border-left: 4px solid #ff9800;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.alert-icon {
  width: 48px;
  height: 48px;
  color: #ff9800;
  flex-shrink: 0;
}

.alert-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--navy-blue);
  margin-bottom: 0.5rem;
}

.alert-text {
  font-size: 1rem;
  color: #546e7a;
  line-height: 1.6;
}

/* Benefits Section */
.benefits-section {
  padding: 5rem 0;
  background: white;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--navy-blue);
  text-align: center;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #78909c;
  text-align: center;
  margin-bottom: 3rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.benefit-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.benefit-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--health-green);
}

.benefit-icon-wrapper {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--health-green), var(--health-green-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: transform 0.3s ease;
}

.benefit-card:hover .benefit-icon-wrapper {
  transform: rotate(360deg);
}

.benefit-icon {
  width: 40px;
  height: 40px;
  color: white;
}

.benefit-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--navy-blue);
  margin-bottom: 0.75rem;
}

.benefit-description {
  font-size: 1rem;
  color: #546e7a;
  line-height: 1.6;
}

.benefits-image-wrapper {
  text-align: center;
  margin-top: 3rem;
}

.benefits-image {
  max-width: 600px;
  width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

/* How It Works Section */
.how-it-works-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #e8f5e9 0%, #f5f7fa 100%);
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.step-card {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--health-green), var(--health-green-light));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.step-card:hover::before {
  transform: scaleX(1);
}

.step-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--health-green), var(--health-green-light));
  color: white;
  font-size: 2rem;
  font-weight: 700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.step-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--navy-blue);
  margin-bottom: 1rem;
}

.step-description {
  font-size: 1rem;
  color: #546e7a;
  line-height: 1.6;
}

/* Pricing Section */
.pricing-section {
  padding: 5rem 0;
  background: white;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1100px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 16px;
  padding: 2.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: var(--health-green);
}

.pricing-card.featured {
  border-color: var(--health-green);
  border-width: 3px;
  box-shadow: 0 8px 24px rgba(124, 179, 66, 0.2);
}

.featured-badge {
  position: absolute;
  top: -12px;
  right: 20px;
  background: var(--health-green);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.pricing-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #f5f5f5;
}

.pricing-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--navy-blue);
  margin-bottom: 1rem;
}

.pricing-price {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 0.25rem;
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--health-green);
  margin-top: 0.5rem;
}

.amount {
  font-size: 4rem;
  font-weight: 800;
  color: var(--navy-blue);
  line-height: 1;
}

.period {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--health-green);
  margin-top: 0.5rem;
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.pricing-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  color: #546e7a;
  font-size: 1rem;
}

.feature-icon {
  width: 20px;
  height: 20px;
  color: var(--health-green);
  flex-shrink: 0;
}

.pricing-button {
  width: 100%;
  background: var(--navy-blue);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pricing-button:hover {
  background: var(--navy-blue-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
}

.featured-button {
  background: var(--health-green);
}

.featured-button:hover {
  background: var(--health-green-dark);
  box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
}

/* Contact Section */
.contact-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info {
  padding-right: 2rem;
}

.contact-description {
  font-size: 1.1rem;
  color: #546e7a;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.contact-method:hover {
  transform: translateX(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.contact-icon {
  width: 32px;
  height: 32px;
  color: var(--health-green);
  flex-shrink: 0;
}

.contact-label {
  font-size: 0.85rem;
  color: #78909c;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.contact-value {
  font-size: 1.1rem;
  color: var(--navy-blue);
  font-weight: 600;
}

.contact-form-wrapper {
  background: white;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--navy-blue);
}

.form-input,
.form-textarea {
  padding: 0.875rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--health-green);
  box-shadow: 0 0 0 3px rgba(124, 179, 66, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-submit {
  background: var(--health-green);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.form-submit:hover {
  background: var(--health-green-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(124, 179, 66, 0.3);
}

/* Footer */
.footer {
  background: var(--navy-blue);
  color: white;
  padding: 3rem 0 1.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo {
  height: 50px;
  width: auto;
  filter: brightness(0) invert(1);
}

.footer-text {
  color: #b0bec5;
  line-height: 1.6;
}

.footer-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  color: #b0bec5;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--health-green-light);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #90a4ae;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap;
  }
  
  .nav {
    display: none;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .hero-buttons {
    flex-direction: column;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .steps-grid {
    grid-template-columns: 1fr;
  }
  
  .pricing-grid {
    grid-template-columns: 1fr;
  }
  
  .alert-box {
    flex-direction: column;
    gap: 1rem;
  }
  
  .contact-info {
    padding-right: 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
  
  .hero {
    padding: 3rem 0;
  }
  
  .hero-title {
    font-size: 1.75rem;
  }
  
  .amount {
    font-size: 3rem;
  }
  
  .contact-form-wrapper {
    padding: 1.5rem;
  }
}
